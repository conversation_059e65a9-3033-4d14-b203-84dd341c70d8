<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName taiga.oatechnologies.us
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/taiga_oatechnologies

    <Directory /var/www/taiga_oatechnologies/>
        Require all granted
        AllowOverride All
        Options FollowSymLinks MultiViews
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/taiga_oatechnologies_error.log
    CustomLog ${APACHE_LOG_DIR}/taiga_oatechnologies_access.log combined

    # Redirect HTTP to HTTPS
    RewriteEngine on
# Some rewrite rules in this file were disabled on your HTTPS site,
# because they have the potential to create redirection loops.

#     RewriteCond %{SERVER_NAME} =taiga.oatechnologies.us
#     RewriteRule ^ https://%{SERVER_NAME}%{REQUEST_URI} [END,NE,R=permanent]


SSLCertificateFile /etc/letsencrypt/live/taiga.oatechnologies.us/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/taiga.oatechnologies.us/privkey.pem
Include /etc/letsencrypt/options-ssl-apache.conf
</VirtualHost>
</IfModule>
