<VirtualHost *:80>
    ServerName taiga.oatechnologies.us
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/taiga_oatechnologies

    <Directory /var/www/taiga_oatechnologies/>
        Require all granted
        AllowOverride All
        Options FollowSymLinks MultiViews
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/taiga_oatechnologies_error.log
    CustomLog ${APACHE_LOG_DIR}/taiga_oatechnologies_access.log combined

    # Redirect HTTP to HTTPS
    RewriteEngine on
    RewriteCond %{SERVER_NAME} =taiga.oatechnologies.us
    RewriteRule ^ https://%{SERVER_NAME}%{REQUEST_URI} [END,NE,R=permanent]
</VirtualHost>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
