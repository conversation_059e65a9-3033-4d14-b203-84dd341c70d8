<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName git.oatechnologies.us
    ServerAdmin webmaster@localhost

    # GitLab Proxy Configuration
    ProxyPreserveHost On
    ProxyRequests Off
    
    # Proxy all requests to GitLab
    ProxyPass / http://127.0.0.1:8080/
    ProxyPassReverse / http://127.0.0.1:8080/
    
    # Handle WebSocket connections for GitLab
    ProxyPass /cable ws://127.0.0.1:8080/cable
    ProxyPassReverse /cable ws://127.0.0.1:8080/cable
    
    # Set headers for GitLab
    ProxyPassReverse / http://git.oatechnologies.us/
    RequestHeader set X-Forwarded-Proto "https"
    RequestHeader set X-Forwarded-Ssl on

    ErrorLog ${APACHE_LOG_DIR}/git_oatechnologies_error.log
    CustomLog ${APACHE_LOG_DIR}/git_oatechnologies_access.log combined

SSLCertificateFile /etc/letsencrypt/live/git.oatechnologies.us/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/git.oatechnologies.us/privkey.pem
Include /etc/letsencrypt/options-ssl-apache.conf
</VirtualHost>
</IfModule>


